import{A as ee,C as H,X as N,Y as v,m as h,K as k,_ as e,$ as ae,D as se,F as te,G as A,H as F,L as f,b as a,I as le,a2 as oe,V as y,J as E,Q,W as U,t as re,M as W,a3 as ie,a4 as ne,a5 as de}from"./SpinnerAugment-AffdR7--.js";import{e as ce,i as ue}from"./IconButtonAugment-DVt24OaC.js";import{A as ve,D as d}from"./index-CnLsnTY6.js";import{B as J}from"./ButtonAugment-K-zrKZyw.js";import{C as he}from"./chevron-down-BBAM6A1q.js";import{T as fe}from"./CardAugment-CB88N7dm.js";import{R}from"./message-broker-Bv_1VsFe.js";var pe=E('<div class="c-dropdown-label svelte-9n7h82"><!></div>'),me=E("<!> <!>",1),$e=E("<!> <!>",1),ge=E("<!> <!>",1);function Se(K,T){ee(T,!1);const[Y,V]=oe(),p=()=>ne(e(C),"$focusedIndex",Y),w=h(),S=h(),s=h();let X=H(T,"onSave",8),i=H(T,"rule",8);const x=[{label:"Always",value:R.ALWAYS_ATTACHED,description:"These Rules will be included in every message you send to the agent."},{label:"Manual",value:R.MANUAL,description:"These Rules will be included when manually tagged in your message. You can tag Rules by @-mentioning them."},{label:"Auto",value:R.AGENT_REQUESTED,description:"These Rules will be included when the Agent decides to fetch them based on this file's description."}];let C=h(void 0),I=h(()=>{});N(()=>k(i()),()=>{v(w,i().path)}),N(()=>k(i()),()=>{v(S,i().type)}),N(()=>e(S),()=>{v(s,x.find(t=>t.value===e(S)))}),ae(),se();var q=te(),j=A(q),O=t=>{fe(t,{content:"Workspace guidelines are always applied",children:(r,B)=>{J(r,{color:"accent",size:1,disabled:!0,children:(m,D)=>{var L=y("Always");a(m,L)},$$slots:{default:!0}})},$$slots:{default:!0}})},P=t=>{d.Root(t,{get requestClose(){return e(I)},set requestClose(r){v(I,r)},get focusedIndex(){return e(C)},set focusedIndex(r){de(v(C,r),"$focusedIndex",Y)},children:(r,B)=>{var m=ge(),D=A(m);d.Trigger(D,{children:(M,Z)=>{var c=pe(),$=re(c);J($,{color:"neutral",size:1,variant:"soft",children:(u,_)=>{var l=y();Q(()=>U(l,(e(s),f(()=>e(s).label)))),a(u,l)},$$slots:{default:!0,iconRight:(u,_)=>{he(u,{slot:"iconRight"})}}}),a(M,c)},$$slots:{default:!0}});var L=W(D,2);d.Content(L,{side:"bottom",align:"start",children:(M,Z)=>{var c=$e(),$=A(c);ce($,1,()=>x,ue,(l,o)=>{const g=ie(()=>(e(s),e(o),f(()=>e(s).label===e(o).label)));d.Item(l,{onSelect:()=>async function(n){e(I)();try{await X()(n.value,n.value!==R.AGENT_REQUESTED||i().description?i().description:"Example description")}catch(b){console.error("RulesModeSelector: Error in onSave:",b)}}(e(o)),get highlight(){return e(g)},children:(n,b)=>{var G=y();Q(()=>U(G,(e(o),f(()=>e(o).label)))),a(n,G)},$$slots:{default:!0}})});var u=W($,2),_=l=>{var o=me(),g=A(o);d.Separator(g,{});var n=W(g,2);d.Label(n,{children:(b,G)=>{var z=y();Q(()=>U(z,(p(),e(s),f(()=>p()!==void 0?x[p()].description:e(s).description)))),a(b,z)},$$slots:{default:!0}}),a(l,o)};F(u,l=>{(p()!==void 0||e(s))&&l(_)}),a(M,c)},$$slots:{default:!0}}),a(r,m)},$$slots:{default:!0},$$legacy:!0})};F(j,t=>{e(w),f(()=>e(w)===ve)?t(O):t(P,!1)}),a(K,q),le(),V()}export{Se as R};
