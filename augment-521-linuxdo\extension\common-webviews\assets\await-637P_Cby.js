import{i as E,c as S,d as j,U,e as d,s as b,m as g,q as F,g as y,h as z,j as A,k as G,r as c,n as h,p as m,o as H}from"./SpinnerAugment-AffdR7--.js";function J(B,C,p,q,u){var a,n,i,t=B,w=E(),D=G,l=U,v=w?b(void 0):g(void 0,!1,!1),f=w?b(void 0):g(void 0,!1,!1),e=!1;function r(s,o){e=!0,o&&(y(x),z(x),A(D));try{s===0&&p&&(a?c(a):a=h(()=>p(t))),s===1&&q&&(n?c(n):n=h(()=>q(t,v))),s===2&&u&&(i?c(i):i=h(()=>u(t,f))),s!==0&&a&&m(a,()=>a=null),s!==1&&n&&m(n,()=>n=null),s!==2&&i&&m(i,()=>i=null)}finally{o&&(A(null),z(null),y(null),H())}}var x=S(()=>{if(l!==(l=C())){if(j(l)){var s=l;e=!1,s.then(o=>{s===l&&(d(v,o),r(1,!0))},o=>{if(s===l&&(d(f,o),r(2,!0),!u))throw f.v}),F(()=>{e||r(0,!0)})}else d(v,l),r(1,!1);return()=>l=U}})}export{J as a};
