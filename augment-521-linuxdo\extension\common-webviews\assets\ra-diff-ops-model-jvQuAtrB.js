var g=Object.defineProperty;var y=(n,e,a)=>e in n?g(n,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):n[e]=a;var o=(n,e,a)=>y(n,typeof e!="symbol"?e+"":e,a);import{W as t}from"./IconButtonAugment-DVt24OaC.js";import{w as p}from"./SpinnerAugment-AffdR7--.js";class u{constructor(e){o(this,"_applyingFilePaths",p([]));o(this,"_appliedFilePaths",p([]));this._asyncMsgSender=e}get applyingFilePaths(){let e=[];return this._applyingFilePaths.subscribe(a=>{e=a})(),e}get appliedFilePaths(){let e=[];return this._appliedFilePaths.subscribe(a=>{e=a})(),e}async getDiffExplanation(e,a,r=3e4){try{return(await this._asyncMsgSender.send({type:t.diffExplanationRequest,data:{changedFiles:e,apikey:a}},r)).data.explanation}catch(s){return console.error("Failed to get diff explanation:",s),[]}}async groupChanges(e,a=!1,r){try{return(await this._asyncMsgSender.send({type:t.diffGroupChangesRequest,data:{changedFiles:e,changesById:a,apikey:r}})).data.groupedChanges}catch(s){return console.error("Failed to group changes:",s),[]}}async getDescriptions(e,a){try{const r=await this._asyncMsgSender.send({type:t.diffDescriptionsRequest,data:{groupedChanges:e,apikey:a}},1e5);return{explanation:r.data.explanation,error:r.data.error}}catch(r){return console.error("Failed to get descriptions:",r),{explanation:[],error:`Failed to get descriptions: ${r instanceof Error?r.message:String(r)}`}}}async canApplyChanges(){try{return(await this._asyncMsgSender.send({type:t.canApplyChangesRequest},1e4)).data}catch(e){return console.error("Failed to check if can apply changes:",e),{canApply:!1,hasUnstagedChanges:!1,error:`Failed to check if can apply changes: ${e instanceof Error?e.message:String(e)}`}}}async applyChanges(e,a,r){this._applyingFilePaths.update(s=>[...s.filter(i=>i!==e),e]);try{const s=await this._asyncMsgSender.send({type:t.applyChangesRequest,data:{path:e,originalCode:a,newCode:r}},3e4),{success:i,hasConflicts:l,error:c}=s.data;return i?this._appliedFilePaths.update(d=>[...d.filter(h=>h!==e),e]):c&&console.error("Failed to apply changes:",c),{success:i,hasConflicts:l,error:c}}catch(s){return console.error("applyChanges error",s),{success:!1,error:`Error: ${s instanceof Error?s.message:String(s)}`}}finally{this._applyingFilePaths.update(s=>s.filter(i=>i!==e))}}async previewApplyChanges(e,a,r){try{return(await this._asyncMsgSender.send({type:t.previewApplyChangesRequest,data:{path:e,originalCode:a,newCode:r}},3e4)).data}catch(s){return console.error("previewApplyChanges error",s),{mergedContent:"",hasConflicts:!1,error:`Error: ${s instanceof Error?s.message:String(s)}`}}}async openFile(e){try{const a=await this._asyncMsgSender.send({type:t.openFileRequest,data:{path:e}},1e4);return a.data.success||console.error("Failed to open file:",a.data.error),a.data.success}catch(a){console.error("openFile error",a)}return!1}async stashUnstagedChanges(){try{const e=await this._asyncMsgSender.send({type:t.stashUnstagedChangesRequest},1e4);return e.data.success||console.error("Failed to stash unstaged changes:",e.data.error),e.data.success}catch(e){console.error("stashUnstagedChanges error",e)}return!1}async reportApplyChangesEvent(){await this._asyncMsgSender.send({type:t.reportAgentChangesApplied})}}o(u,"key","remoteAgentsDiffOpsModel");export{u as R};
