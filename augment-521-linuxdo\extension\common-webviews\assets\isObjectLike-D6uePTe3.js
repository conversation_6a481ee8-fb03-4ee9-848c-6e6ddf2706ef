import{x as r}from"./SpinnerAugment-AffdR7--.js";var x=function(t){var o=typeof t;return t!=null&&(o=="object"||o=="function")},b=typeof r=="object"&&r&&r.Object===Object&&r,j=b,s=typeof self=="object"&&self&&self.Object===Object&&self,v=j||s||Function("return this")(),n=v.Symbol,c=n,i=Object.prototype,p=i.hasOwnProperty,y=i.toString,e=c?c.toStringTag:void 0,d=function(t){var o=p.call(t,e),l=t[e];try{t[e]=void 0;var u=!0}catch{}var f=y.call(t);return u&&(o?t[e]=l:delete t[e]),f},O=Object.prototype.toString,g=d,S=function(t){return O.call(t)},a=n?n.toStringTag:void 0,T=function(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":a&&a in Object(t)?g(t):S(t)},w=function(t){return t!=null&&typeof t=="object"};export{T as _,v as a,n as b,w as c,b as d,x as i};
