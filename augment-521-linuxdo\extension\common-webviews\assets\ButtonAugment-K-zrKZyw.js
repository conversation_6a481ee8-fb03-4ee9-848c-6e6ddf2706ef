import{z as D,l as z,C as i,a7 as E,J as u,H as g,L as h,t as c,M as p,Q as K,ab as O,b as r,N as m,T as P,F as S,G as U,_ as y,a3 as C}from"./SpinnerAugment-AffdR7--.js";import{B as V,b as e}from"./IconButtonAugment-DVt24OaC.js";var W=u('<div class="c-button--icon svelte-14satks"><!></div>'),X=u('<div class="c-button--text svelte-14satks"><!></div>'),Y=u('<div class="c-button--icon svelte-14satks"><!></div>'),Z=u("<div><!> <!> <!></div>");function et(x,t){const d=D(t),B=z(t,["children","$$slots","$$events","$$legacy"]),L=z(B,["size","variant","color","highContrast","disabled","radius","loading","alignment"]);let o=i(t,"size",8,2),$=i(t,"variant",8,"solid"),R=i(t,"color",8,"neutral"),F=i(t,"highContrast",8,!1),G=i(t,"disabled",8,!1),H=i(t,"radius",8,"medium"),J=i(t,"loading",8,!1),j=i(t,"alignment",8,"center");V(x,E({get size(){return o()},get variant(){return $()},get color(){return R()},get highContrast(){return F()},get disabled(){return G()},get loading(){return J()},get alignment(){return j()},get radius(){return H()}},()=>L,{$$events:{click(s){e.call(this,t,s)},keyup(s){e.call(this,t,s)},keydown(s){e.call(this,t,s)},mousedown(s){e.call(this,t,s)},mouseover(s){e.call(this,t,s)},focus(s){e.call(this,t,s)},mouseleave(s){e.call(this,t,s)},blur(s){e.call(this,t,s)},contextmenu(s){e.call(this,t,s)}},children:(s,w)=>{var v=Z(),b=c(v),A=a=>{var l=W(),n=c(l);m(n,t,"iconLeft",{},null),r(a,l)};g(b,a=>{h(()=>d.iconLeft)&&a(A)});var f=p(b,2),I=a=>{var l=X(),n=c(l);const Q=C(()=>o()===.5?1:o()),T=C(()=>$()==="ghost"?"regular":"medium");P(n,{get size(){return y(Q)},get weight(){return y(T)},children:(_,tt)=>{var k=S(),q=U(k);m(q,t,"default",{},null),r(_,k)},$$slots:{default:!0}}),r(a,l)};g(f,a=>{h(()=>d.default)&&a(I)});var M=p(f,2),N=a=>{var l=Y(),n=c(l);m(n,t,"iconRight",{},null),r(a,l)};g(M,a=>{h(()=>d.iconRight)&&a(N)}),K(()=>O(v,1,`c-button--content c-button--size-${o()}`,"svelte-14satks")),r(s,v)},$$slots:{default:!0}}))}export{et as B};
