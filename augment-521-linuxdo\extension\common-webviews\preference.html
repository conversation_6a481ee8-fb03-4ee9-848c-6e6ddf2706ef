<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Preference</title>
    <script nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <script type="module" crossorigin src="./assets/preference-D_FtgF1W.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-AffdR7--.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-DVt24OaC.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-4ajbBCm_.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-CB88N7dm.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-DXXiLgz5.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-Bv_1VsFe.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/focusTrapStack-CB_5BS9R.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-D6uePTe3.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-IcL3sG2L.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/index-CnLsnTY6.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-a0bLStc3.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/index-C5DcjNTh.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/svelte-component-BzMfvILK.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-CLvGlhI3.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-Cb9MCs64.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/index-ZbZe59K6.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/diff-operations-zlr5gNTh.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/await-637P_Cby.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/chat-model-context-LQjgzAXk.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/index-C4gKbsWy.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/remote-agents-client-DXcDoFHp.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-jvQuAtrB.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-K-zrKZyw.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-BXNDUf24.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/OpenFileButton-DLmRCR9z.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-DgGSnbBS.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/user-BlpcIo5U.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-DgNf9Yde.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/CopyButton-Cx19Z4lO.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-BHLqUIzX.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-CVD96Tjf.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/next-edit-types-904A5ehg.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/IconFilePath-Bz0iFBtq.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-BBAM6A1q.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/index-oHUUsc-1.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-Dea9S5F6.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-ChHviosp.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/check-CEaHRvsZ.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="modulepreload" crossorigin href="./assets/AugmentMessage-GSkxqNfK.js" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/AugmentMessage-CV9rFp-1.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-CEzLOEg8.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/folder-opened-hTsrGIsd.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-C-z-uXWx.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-DRIZURB3.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/index-D9au8v71.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/diff-operations-MyOXHVsl.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-CMEPAZfs.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/OpenFileButton-bH4F3VXH.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-k8sG2hbx.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-BcSV_kHI.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-BVldpZ4-.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/user-dXUx3CYB.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/CopyButton-B0_wR17F.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/IconFilePath-BVaLv7mP.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
    <link rel="stylesheet" crossorigin href="./assets/preference-CUbmjS6T.css" nonce="nonce-9SkbGT4nTRgOPQyY4fZWXg==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
